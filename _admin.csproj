<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.TypeScript.MSBuild.4.7.4\build\Microsoft.TypeScript.MSBuild.props" Condition="Exists('..\packages\Microsoft.TypeScript.MSBuild.4.7.4\build\Microsoft.TypeScript.MSBuild.props')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{CF65DB5F-4316-4F17-B589-D7B7850313F2}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiMS.AdminPanel</RootNamespace>
    <AssemblyName>admin</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <LangVersion>8.0</LangVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>
    </CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreGeneratedCode>true</CodeAnalysisIgnoreGeneratedCode>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>
    </CodeAnalysisRuleSet>
    <PublishDatabases>false</PublishDatabases>
    <LegacyPublishPropertiesPageEnabled>true</LegacyPublishPropertiesPageEnabled>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Content Include=".gitignore" />
    <Content Include="assets\css\Jcrop.gif" />
    <Content Include="assets\css\jquery.Jcrop.css" />
    <Content Include="assets\css\jquery.Jcrop.min.css" />
    <Content Include="assets\img\blue-rounded-rectangle.svg" />
    <Content Include="assets\img\rounded-rectangle.svg" />
    <Content Include="assets\img\user-blank.jpg" />
    <Content Include="assets\plugins\bootstrap-growl.min.js" />
    <Content Include="assets\plugins\jquery.Jcrop.js" />
    <Content Include="assets\plugins\jquery.Jcrop.min.js" />
    <Content Include="cntrls\_cntrl.modal.edit_instance_info.ascx" />
    <Content Include="cntrls\_cntrl.modal.credit_note.ascx" />
    <Content Include="cntrls\_cntrl.modal.new_instance.ascx" />
    <Content Include="cntrls\_cntrl.profile_pic.crop.ascx" />
    <Content Include="error.aspx" />
    <Content Include="favicon.ico" />
    <Content Include="license\index.aspx" />
    <Content Include="pwdc.aspx" />
    <Content Include="pwdr.aspx" />
    <Content Include="session_timeout.aspx" />
    <Content Include="ViewSwitcher.ascx" />
    <Content Include="_admin\signup.aspx" />
    <Content Include="_admin\signups.aspx" />
    <Content Include="_billing\billables.aspx" />
    <Content Include="_billing\invoice.aspx" />
    <Content Include="_billing\invoices.aspx" />
    <Content Include="_billing\invoice_print.aspx" />
    <Content Include="_billing\credit_notes.aspx" />
    <Content Include="cntrls\_cntrl.modal.invoice.edit.ascx" />
    <Content Include="cntrls\_cntrl.modal.invoice_payment.ascx" />
    <Content Include="_dashboards\instance.aspx" />
    <Content Include="_dashboards\main.aspx" />
    <Content Include="_datawarehouse\attendance.aspx" />
    <Content Include="_datawarehouse\tables.aspx" />
    <Content Include="_datawarehouse\enrolment.aspx" />
    <Content Include="_datawarehouse\exam_results.aspx" />
    <Content Include="_datawarehouse\index.aspx" />
    <Content Include="_datawarehouse\instances.aspx" />
    <Content Include="_datawarehouse\schedule.aspx" />
    <Content Include="_admin\demos.aspx" />
    <Content Include="_admin\models.aspx" />
    <Content Include="_admin\users.aspx" />
    <Content Include="_admin\sync_instances.aspx" />
    <Content Include="_main\my_profile.aspx" />
    <Content Include="_main\server_mon.aspx" />
    <Content Include="_manage\db_procedures.aspx" />
    <Content Include="_main\error.aspx" />
    <Content Include="cntrls\_cntrl.modal.sms.ascx" />
    <Content Include="_admin\model_instance.aspx" />
    <Content Include="_admin\new.aspx" />
    <Content Include="txt\release_notes.txt" />
    <Content Include="_admin\regions.aspx" />
    <Content Include="_manage\settings.aspx" />
    <Content Include="_manage\company.aspx" />
    <Content Include="_billing\statement.aspx" />
    <Content Include="_billing\print_statement.aspx" />
    <Content Include="_billing\statements.aspx" />
    <Content Include="global.asax" />
    <Content Include="index.aspx" />
    <Content Include="login.aspx" />
    <Content Include="cntrls\_cntrl.controlpannel.ascx" />
    <Content Include="_tpl\billing_payment_receipt.html" />
    <Content Include="_tpl\tpl.password.reset.html" />
    <Content Include="cntrls\_cntrl.modal.file_viewer.ascx" />
    <Content Include="web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="_admin\instance.aspx" />
    <Content Include="_admin\instances.aspx" />
    <Content Include="_main\index.aspx" />
    <Content Include="_manage\hosts.aspx" />
    <Content Include="_manage\users.aspx" />
    <Content Include="cntrls\_cntrl.modal.edit_setting.ascx" />
    <Content Include="_tpl\app_offline.htm">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="_tpl\email.login_otp.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="_tpl\email.password_reset.confirm.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="_tpl\email.password_reset.html" />
    <Content Include="_tpl\email.password_reset.token.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="_tpl\email.userdetails.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="_tpl\receipt.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="cntrls\_cntrl.modal.new_instance.ascx.cs">
      <DependentUpon>_cntrl.modal.new_instance.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.new_instance.ascx.designer.cs">
      <DependentUpon>_cntrl.modal.new_instance.ascx</DependentUpon>
    </Compile>
    <Compile Include="cntrls\_cntrl.profile_pic.crop.ascx.cs">
      <DependentUpon>_cntrl.profile_pic.crop.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.profile_pic.crop.ascx.designer.cs">
      <DependentUpon>_cntrl.profile_pic.crop.ascx</DependentUpon>
    </Compile>
    <Compile Include="cs\bind_controls.cs" />
    <Compile Include="cs\db.fetch.cs" />
    <Compile Include="cs\db.execute.cs" />
    <Compile Include="hook\signup.ashx.cs">
      <DependentUpon>signup.ashx</DependentUpon>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.edit_instance_info.ascx.cs">
      <DependentUpon>_cntrl.modal.edit_instance_info.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.edit_instance_info.ascx.designer.cs">
      <DependentUpon>_cntrl.modal.edit_instance_info.ascx</DependentUpon>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.credit_note.ascx.cs">
      <DependentUpon>_cntrl.modal.credit_note.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.credit_note.ascx.designer.cs">
      <DependentUpon>_cntrl.modal.credit_note.ascx</DependentUpon>
    </Compile>
    <Compile Include="cs\billing.cs" />
    <Compile Include="cs\routes-conf.cs" />
    <Compile Include="cs\db.connection.cs" />
    <Compile Include="cs\_sms.cs" />
    <Compile Include="error.aspx.cs">
      <DependentUpon>error.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="error.aspx.designer.cs">
      <DependentUpon>error.aspx</DependentUpon>
    </Compile>
    <Compile Include="license\index.aspx.cs">
      <DependentUpon>index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="license\index.aspx.designer.cs">
      <DependentUpon>index.aspx</DependentUpon>
    </Compile>
    <Compile Include="license\host.ashx.cs">
      <DependentUpon>host.ashx</DependentUpon>
    </Compile>
    <Compile Include="license\instance.ashx.cs">
      <DependentUpon>instance.ashx</DependentUpon>
    </Compile>
    <Compile Include="pwdc.aspx.cs">
      <DependentUpon>pwdc.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="pwdc.aspx.designer.cs">
      <DependentUpon>pwdc.aspx</DependentUpon>
    </Compile>
    <Compile Include="pwdr.aspx.cs">
      <DependentUpon>pwdr.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="pwdr.aspx.designer.cs">
      <DependentUpon>pwdr.aspx</DependentUpon>
    </Compile>
    <Compile Include="session_timeout.aspx.cs">
      <DependentUpon>session_timeout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="session_timeout.aspx.designer.cs">
      <DependentUpon>session_timeout.aspx</DependentUpon>
    </Compile>
    <Compile Include="Site.Mobile.Master.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
    </Compile>
    <Compile Include="Site.Mobile.Master.designer.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
    </Compile>
    <Compile Include="sync\instance.ashx.cs">
      <DependentUpon>instance.ashx</DependentUpon>
    </Compile>
    <Compile Include="sync\invoices.ashx.cs">
      <DependentUpon>invoices.ashx</DependentUpon>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.designer.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
    <Compile Include="_admin\signup.aspx.cs">
      <DependentUpon>signup.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\signup.aspx.designer.cs">
      <DependentUpon>signup.aspx</DependentUpon>
    </Compile>
    <Compile Include="_admin\signups.aspx.cs">
      <DependentUpon>signups.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\signups.aspx.designer.cs">
      <DependentUpon>signups.aspx</DependentUpon>
    </Compile>
    <Compile Include="_billing\billables.aspx.cs">
      <DependentUpon>billables.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\billables.aspx.designer.cs">
      <DependentUpon>billables.aspx</DependentUpon>
    </Compile>
    <Compile Include="_billing\invoice.aspx.cs">
      <DependentUpon>invoice.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\invoice.aspx.designer.cs">
      <DependentUpon>invoice.aspx</DependentUpon>
    </Compile>
    <Compile Include="_billing\invoices.aspx.cs">
      <DependentUpon>invoices.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\invoices.aspx.designer.cs">
      <DependentUpon>invoices.aspx</DependentUpon>
    </Compile>
    <Compile Include="_billing\invoice_print.aspx.cs">
      <DependentUpon>invoice_print.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\invoice_print.aspx.designer.cs">
      <DependentUpon>invoice_print.aspx</DependentUpon>
    </Compile>
    <Compile Include="_billing\credit_notes.aspx.cs">
      <DependentUpon>credit_notes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\credit_notes.aspx.designer.cs">
      <DependentUpon>credit_notes.aspx</DependentUpon>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.invoice.edit.ascx.cs">
      <DependentUpon>_cntrl.modal.invoice.edit.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.invoice.edit.ascx.designer.cs">
      <DependentUpon>_cntrl.modal.invoice.edit.ascx</DependentUpon>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.invoice_payment.ascx.cs">
      <DependentUpon>_cntrl.modal.invoice_payment.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.invoice_payment.ascx.designer.cs">
      <DependentUpon>_cntrl.modal.invoice_payment.ascx</DependentUpon>
    </Compile>
    <Compile Include="_dashboards\dashboard.master.cs">
      <DependentUpon>dashboard.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_dashboards\dashboard.master.designer.cs">
      <DependentUpon>dashboard.master</DependentUpon>
    </Compile>
    <Compile Include="_dashboards\instance.aspx.cs">
      <DependentUpon>instance.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_dashboards\instance.aspx.designer.cs">
      <DependentUpon>instance.aspx</DependentUpon>
    </Compile>
    <Compile Include="_dashboards\main.aspx.cs">
      <DependentUpon>main.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_dashboards\main.aspx.designer.cs">
      <DependentUpon>main.aspx</DependentUpon>
    </Compile>
    <Compile Include="_dashboards\_json.fullcalendar.ashx.cs">
      <DependentUpon>_json.fullcalendar.ashx</DependentUpon>
    </Compile>
    <Compile Include="_datawarehouse\attendance.aspx.cs">
      <DependentUpon>attendance.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_datawarehouse\attendance.aspx.designer.cs">
      <DependentUpon>attendance.aspx</DependentUpon>
    </Compile>
    <Compile Include="_datawarehouse\tables.aspx.cs">
      <DependentUpon>tables.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_datawarehouse\tables.aspx.designer.cs">
      <DependentUpon>tables.aspx</DependentUpon>
    </Compile>
    <Compile Include="_datawarehouse\datawarehouse.master.cs">
      <DependentUpon>datawarehouse.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_datawarehouse\datawarehouse.master.designer.cs">
      <DependentUpon>datawarehouse.master</DependentUpon>
    </Compile>
    <Compile Include="_datawarehouse\enrolment.aspx.cs">
      <DependentUpon>enrolment.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_datawarehouse\enrolment.aspx.designer.cs">
      <DependentUpon>enrolment.aspx</DependentUpon>
    </Compile>
    <Compile Include="_datawarehouse\exam_results.aspx.cs">
      <DependentUpon>exam_results.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_datawarehouse\exam_results.aspx.designer.cs">
      <DependentUpon>exam_results.aspx</DependentUpon>
    </Compile>
    <Compile Include="_datawarehouse\index.aspx.cs">
      <DependentUpon>index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_datawarehouse\index.aspx.designer.cs">
      <DependentUpon>index.aspx</DependentUpon>
    </Compile>
    <Compile Include="_datawarehouse\instances.aspx.cs">
      <DependentUpon>instances.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_datawarehouse\instances.aspx.designer.cs">
      <DependentUpon>instances.aspx</DependentUpon>
    </Compile>
    <Compile Include="_datawarehouse\schedule.aspx.cs">
      <DependentUpon>schedule.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_datawarehouse\schedule.aspx.designer.cs">
      <DependentUpon>schedule.aspx</DependentUpon>
    </Compile>
    <Compile Include="_admin\demos.aspx.cs">
      <DependentUpon>demos.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\demos.aspx.designer.cs">
      <DependentUpon>demos.aspx</DependentUpon>
    </Compile>
    <Compile Include="_admin\admin.master.cs">
      <DependentUpon>admin.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\admin.master.designer.cs">
      <DependentUpon>admin.master</DependentUpon>
    </Compile>
    <Compile Include="_admin\models.aspx.cs">
      <DependentUpon>models.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\models.aspx.designer.cs">
      <DependentUpon>models.aspx</DependentUpon>
    </Compile>
    <Compile Include="_admin\users.aspx.cs">
      <DependentUpon>users.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\users.aspx.designer.cs">
      <DependentUpon>users.aspx</DependentUpon>
    </Compile>
    <Compile Include="_admin\sync_instances.aspx.cs">
      <DependentUpon>sync_instances.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\sync_instances.aspx.designer.cs">
      <DependentUpon>sync_instances.aspx</DependentUpon>
    </Compile>
    <Compile Include="_main\my_profile.aspx.cs">
      <DependentUpon>my_profile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_main\my_profile.aspx.designer.cs">
      <DependentUpon>my_profile.aspx</DependentUpon>
    </Compile>
    <Compile Include="_main\server_mon.aspx.cs">
      <DependentUpon>server_mon.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_main\server_mon.aspx.designer.cs">
      <DependentUpon>server_mon.aspx</DependentUpon>
    </Compile>
    <Compile Include="_manage\db_procedures.aspx.cs">
      <DependentUpon>db_procedures.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_manage\db_procedures.aspx.designer.cs">
      <DependentUpon>db_procedures.aspx</DependentUpon>
    </Compile>
    <Compile Include="_main\error.aspx.cs">
      <DependentUpon>error.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_main\error.aspx.designer.cs">
      <DependentUpon>error.aspx</DependentUpon>
    </Compile>
    <Compile Include="_main\_main.master.cs">
      <DependentUpon>_main.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_main\_main.master.designer.cs">
      <DependentUpon>_main.master</DependentUpon>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.sms.ascx.cs">
      <DependentUpon>_cntrl.modal.sms.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.sms.ascx.designer.cs">
      <DependentUpon>_cntrl.modal.sms.ascx</DependentUpon>
    </Compile>
    <Compile Include="sync\server.ashx.cs">
      <DependentUpon>server.ashx</DependentUpon>
    </Compile>
    <Compile Include="_admin\model_instance.aspx.cs">
      <DependentUpon>model_instance.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\model_instance.aspx.designer.cs">
      <DependentUpon>model_instance.aspx</DependentUpon>
    </Compile>
    <Compile Include="_admin\new.aspx.cs">
      <DependentUpon>new.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\new.aspx.designer.cs">
      <DependentUpon>new.aspx</DependentUpon>
    </Compile>
    <Compile Include="_admin\regions.aspx.cs">
      <DependentUpon>regions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\regions.aspx.designer.cs">
      <DependentUpon>regions.aspx</DependentUpon>
    </Compile>
    <Compile Include="_manage\settings.aspx.cs">
      <DependentUpon>settings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_manage\settings.aspx.designer.cs">
      <DependentUpon>settings.aspx</DependentUpon>
    </Compile>
    <Compile Include="_manage\manage.master.cs">
      <DependentUpon>manage.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_manage\manage.master.designer.cs">
      <DependentUpon>manage.master</DependentUpon>
    </Compile>
    <Compile Include="_manage\company.aspx.cs">
      <DependentUpon>company.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_manage\company.aspx.designer.cs">
      <DependentUpon>company.aspx</DependentUpon>
    </Compile>
    <Compile Include="_billing\billing.master.cs">
      <DependentUpon>billing.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\billing.master.designer.cs">
      <DependentUpon>billing.master</DependentUpon>
    </Compile>
    <Compile Include="_billing\statement.aspx.cs">
      <DependentUpon>statement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\statement.aspx.designer.cs">
      <DependentUpon>statement.aspx</DependentUpon>
    </Compile>
    <Compile Include="_billing\print_statement.aspx.cs">
      <DependentUpon>print_statement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\print_statement.aspx.designer.cs">
      <DependentUpon>print_statement.aspx</DependentUpon>
    </Compile>
    <Compile Include="_billing\statements.aspx.cs">
      <DependentUpon>statements.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_billing\statements.aspx.designer.cs">
      <DependentUpon>statements.aspx</DependentUpon>
    </Compile>
    <Compile Include="global.asax.cs">
      <DependentUpon>global.asax</DependentUpon>
    </Compile>
    <Compile Include="index.aspx.cs">
      <DependentUpon>index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="index.aspx.designer.cs">
      <DependentUpon>index.aspx</DependentUpon>
    </Compile>
    <Compile Include="login.aspx.cs">
      <DependentUpon>login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="login.aspx.designer.cs">
      <DependentUpon>login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="cntrls\_cntrl.controlpannel.ascx.cs">
      <DependentUpon>_cntrl.controlpannel.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.controlpannel.ascx.designer.cs">
      <DependentUpon>_cntrl.controlpannel.ascx</DependentUpon>
    </Compile>
    <Compile Include="_admin\instance.aspx.cs">
      <DependentUpon>instance.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\instance.aspx.designer.cs">
      <DependentUpon>instance.aspx</DependentUpon>
    </Compile>
    <Compile Include="_admin\instances.aspx.cs">
      <DependentUpon>instances.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_admin\instances.aspx.designer.cs">
      <DependentUpon>instances.aspx</DependentUpon>
    </Compile>
    <Compile Include="_main\index.aspx.cs">
      <DependentUpon>index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_main\index.aspx.designer.cs">
      <DependentUpon>index.aspx</DependentUpon>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.file_viewer.ascx.cs">
      <DependentUpon>_cntrl.modal.file_viewer.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.file_viewer.ascx.designer.cs">
      <DependentUpon>_cntrl.modal.file_viewer.ascx</DependentUpon>
    </Compile>
    <Compile Include="cs\_alert.cs" />
    <Compile Include="cs\_cookie.cs" />
    <Compile Include="cs\_fn.cs" />
    <Compile Include="cs\_ftp.cs" />
    <Compile Include="cs\_http.cs" />
    <Compile Include="cs\_img.cs" />
    <Compile Include="cs\_IP4Info.cs" />
    <Compile Include="cs\_mailer.cs" />
    <Compile Include="cs\_objects.cs" />
    <Compile Include="cs\_pdf.cs" />
    <Compile Include="_manage\hosts.aspx.cs">
      <DependentUpon>hosts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_manage\hosts.aspx.designer.cs">
      <DependentUpon>hosts.aspx</DependentUpon>
    </Compile>
    <Compile Include="_manage\users.aspx.cs">
      <DependentUpon>users.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="_manage\users.aspx.designer.cs">
      <DependentUpon>users.aspx</DependentUpon>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.edit_setting.ascx.cs">
      <DependentUpon>_cntrl.modal.edit_setting.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="cntrls\_cntrl.modal.edit_setting.ascx.designer.cs">
      <DependentUpon>_cntrl.modal.edit_setting.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="_admin\new.bat" />
    <Content Include="license\instance.ashx" />
    <Content Include="libman.json" />
    <Content Include="license\host.ashx" />
    <Content Include="hook\signup.ashx" />
    <Content Include="assets\img\logo.webp" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <Content Include="sync\server.ashx" />
    <Content Include="_main\_main.master" />
    <Content Include="_billing\billing.master" />
    <Content Include="_datawarehouse\datawarehouse.master" />
    <Content Include="_admin\admin.master" />
    <Content Include="_manage\manage.master" />
    <Content Include="sync\invoices.ashx" />
    <Content Include="sync\instance.ashx" />
    <Content Include="_dashboards\_json.fullcalendar.ashx" />
    <Content Include="_dashboards\dashboard.master" />
    <Content Include="Site.Mobile.Master" />
    <None Include="Web.Debug.Config">
      <DependentUpon>web.config</DependentUpon>
    </None>
    <None Include="Web.Release.Config">
      <DependentUpon>web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="obj\src\logs\" />
    <Folder Include="tmp\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>packages\BouncyCastle.Cryptography.2.6.1\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="ChoETL, Version=1.2.1.70, Culture=neutral, PublicKeyToken=8138733ed69d3172, processorArchitecture=MSIL">
      <HintPath>packages\ChoETL.1.2.1.70\lib\net45\ChoETL.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.12.2.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>packages\HtmlAgilityPack.1.12.2\lib\Net45\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="Magick.NET-Q16-AnyCPU, Version=14.7.0.0, Culture=neutral, PublicKeyToken=2004825badfa91ec, processorArchitecture=MSIL">
      <HintPath>packages\Magick.NET-Q16-AnyCPU.14.7.0\lib\netstandard20\Magick.NET-Q16-AnyCPU.dll</HintPath>
    </Reference>
    <Reference Include="Magick.NET.Core, Version=14.7.0.0, Culture=neutral, PublicKeyToken=2004825badfa91ec, processorArchitecture=MSIL">
      <HintPath>packages\Magick.NET.Core.14.7.0\lib\netstandard20\Magick.NET.Core.dll</HintPath>
    </Reference>
    <Reference Include="MailKit, Version=4.13.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b, processorArchitecture=MSIL">
      <HintPath>packages\MailKit.4.13.0\lib\net48\MailKit.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls, Version=1.0.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net45\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.AsyncInterfaces.9.0.7\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.HashCode, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.HashCode.6.0.0\lib\net462\Microsoft.Bcl.HashCode.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Web.Administration, Version=10.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Web.Administration.11.1.0\lib\netstandard1.5\Microsoft.Web.Administration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Win32.Primitives.4.3.0\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Registry, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Win32.Registry.5.0.0\lib\net461\Microsoft.Win32.Registry.dll</HintPath>
    </Reference>
    <Reference Include="MimeKit, Version=4.13.0.0, Culture=neutral, PublicKeyToken=bede1c8a46c66814, processorArchitecture=MSIL">
      <HintPath>packages\MimeKit.4.13.0\lib\net48\MimeKit.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework" />
    <Reference Include="RestSharp, Version=112.1.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>packages\RestSharp.112.1.0\lib\net48\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="Select.HtmlToPdf, Version=25.2.0.0, Culture=neutral, PublicKeyToken=e0ae9f6e27a97018, processorArchitecture=MSIL">
      <HintPath>packages\Select.HtmlToPdf.25.2.0\lib\net40\Select.HtmlToPdf.dll</HintPath>
    </Reference>
    <Reference Include="Standard.Licensing, Version=1.2.1.0, Culture=neutral, PublicKeyToken=9f81b18f6db6aea5, processorArchitecture=MSIL">
      <HintPath>packages\Standard.Licensing.1.2.1\lib\net461\Standard.Licensing.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.AppContext, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.AppContext.4.3.0\lib\net463\System.AppContext.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Buffers.4.6.1\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.CodeDom, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.CodeDom.9.0.7\lib\net462\System.CodeDom.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Collections.Immutable.9.0.7\lib\net462\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Configuration.ConfigurationManager.9.0.7\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Console, Version=4.0.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Console.4.3.1\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.SqlClient, Version=4.6.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Data.SqlClient.4.9.0\lib\net462\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.DiagnosticSource.9.0.7\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.EventLog, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.EventLog.9.0.7\lib\net462\System.Diagnostics.EventLog.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.TraceSource.4.3.0\lib\net46\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.Tracing.4.3.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Formats.Asn1, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Formats.Asn1.9.0.7\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.Formats.Nrbf, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Formats.Nrbf.9.0.7\lib\net462\System.Formats.Nrbf.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Globalization.Calendars.4.3.0\lib\net46\System.Globalization.Calendars.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Compression.ZipFile.4.3.0\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Pipelines.9.0.7\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Linq.4.3.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Linq.Expressions.4.3.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Memory.4.6.3\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.6.1\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Reflection.Metadata, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Reflection.Metadata.9.0.7\lib\net462\System.Reflection.Metadata.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.TypeExtensions, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Reflection.TypeExtensions.4.7.0\lib\net461\System.Reflection.TypeExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Resources.Extensions.9.0.7\lib\net462\System.Resources.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.6.1.2\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.Extensions.4.3.1\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.InteropServices.4.3.0\lib\net463\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=6.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.AccessControl.6.0.1\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Claims.4.3.0\lib\net46\System.Security.Claims.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.ServiceProcess.ServiceController, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.ServiceProcess.ServiceController.9.0.7\lib\net462\System.ServiceProcess.ServiceController.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Encodings.Web.9.0.7\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Json.9.0.7\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.RegularExpressions.4.3.1\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Tasks.Extensions.4.6.3\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml.ReaderWriter, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Xml.ReaderWriter.4.3.1\lib\net46\System.Xml.ReaderWriter.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>57500</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:44303/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <Import Project="..\..\.nuget\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\..\.nuget\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
  <Import Project="..\..\.nuget\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets" Condition="Exists('..\..\.nuget\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" />
  <Import Project="..\..\.nuget\packages\Magick.NET-Q16-AnyCPU.14.6.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets" Condition="Exists('..\..\.nuget\packages\Magick.NET-Q16-AnyCPU.14.6.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <Error Condition="!Exists('packages\Magick.NET-Q16-AnyCPU.14.7.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Magick.NET-Q16-AnyCPU.14.7.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets'))" />
    <Error Condition="!Exists('packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets'))" />
    <Error Condition="!Exists('packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets'))" />
  </Target>
  <Import Project="..\packages\Magick.NET-Q16-AnyCPU.14.7.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets" Condition="Exists('..\packages\Magick.NET-Q16-AnyCPU.14.7.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets')" />
  <Import Project="packages\Magick.NET-Q16-AnyCPU.14.7.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets" Condition="Exists('packages\Magick.NET-Q16-AnyCPU.14.7.0\build\netstandard20\Magick.NET-Q16-AnyCPU.targets')" />
  <Import Project="packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
  <Import Project="packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets" Condition="Exists('packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
/*!
 RowReorder 1.2.6
 2015-2019 SpryMedia Ltd - datatables.net/license
*/
(function(e){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(f){return e(f,window,document)}):"object"===typeof exports?module.exports=function(f,g){f||(f=window);if(!g||!g.fn.dataTable)g=require("datatables.net")(f,g).$;return e(g,f,f.document)}:e(jQuery,window,document)})(function(e,f,g,n){var h=e.fn.dataTable,j=function(d,c){if(!h.versionCheck||!h.versionCheck("1.10.8"))throw"DataTables RowReorder requires DataTables 1.10.8 or newer";this.c=e.extend(!0,{},h.defaults.rowReorder,
j.defaults,c);this.s={bodyTop:null,dt:new h.Api(d),getDataFn:h.ext.oApi._fnGetObjectDataFn(this.c.dataSrc),middles:null,scroll:{},scrollInterval:null,setDataFn:h.ext.oApi._fnSetObjectDataFn(this.c.dataSrc),start:{top:0,left:0,offsetTop:0,offsetLeft:0,nodes:[]},windowHeight:0,documentOuterHeight:0,domCloneOuterHeight:0};this.dom={clone:null,dtScroll:e("div.dataTables_scrollBody",this.s.dt.table().container())};var b=this.s.dt.settings()[0],a=b.rowreorder;if(a)return a;b.rowreorder=this;this._constructor()};
e.extend(j.prototype,{_constructor:function(){var d=this,c=this.s.dt,b=e(c.table().node());"static"===b.css("position")&&b.css("position","relative");e(c.table().container()).on("mousedown.rowReorder touchstart.rowReorder",this.c.selector,function(a){if(d.c.enable){if(e(a.target).is(d.c.excludedChildren))return!0;var b=e(this).closest("tr"),k=c.row(b);if(k.any())return d._emitEvent("pre-row-reorder",{node:k.node(),index:k.index()}),d._mouseDown(a,b),!1}});c.on("destroy.rowReorder",function(){e(c.table().container()).off(".rowReorder");
c.off(".rowReorder")})},_cachePositions:function(){var d=this.s.dt,c=e(d.table().node()).find("thead").outerHeight(),b=e.unique(d.rows({page:"current"}).nodes().toArray()),a=e.map(b,function(a){return e(a).position().top-c}),b=e.map(a,function(b,c){return a.length<c-1?(b+a[c+1])/2:(b+b+e(d.row(":last-child").node()).outerHeight())/2});this.s.middles=b;this.s.bodyTop=e(d.table().body()).offset().top;this.s.windowHeight=e(f).height();this.s.documentOuterHeight=e(g).outerHeight()},_clone:function(d){var c=
e(this.s.dt.table().node().cloneNode(!1)).addClass("dt-rowReorder-float").append("<tbody/>").append(d.clone(!1)),b=d.outerWidth(),a=d.outerHeight(),i=d.children().map(function(){return e(this).width()});c.width(b).height(a).find("tr").children().each(function(b){this.style.width=i[b]+"px"});c.appendTo("body");this.dom.clone=c;this.s.domCloneOuterHeight=c.outerHeight()},_clonePosition:function(d){var c=this.s.start,b=this._eventToPage(d,"Y")-c.top,d=this._eventToPage(d,"X")-c.left,a=this.c.snapX,b=
b+c.offsetTop,c=!0===a?c.offsetLeft:"number"===typeof a?c.offsetLeft+a:d+c.offsetLeft;0>b?b=0:b+this.s.domCloneOuterHeight>this.s.documentOuterHeight&&(b=this.s.documentOuterHeight-this.s.domCloneOuterHeight);this.dom.clone.css({top:b,left:c})},_emitEvent:function(d,c){this.s.dt.iterator("table",function(b){e(b.nTable).triggerHandler(d+".dt",c)})},_eventToPage:function(d,c){return-1!==d.type.indexOf("touch")?d.originalEvent.touches[0]["page"+c]:d["page"+c]},_mouseDown:function(d,c){var b=this,a=this.s.dt,
i=this.s.start,k=c.offset();i.top=this._eventToPage(d,"Y");i.left=this._eventToPage(d,"X");i.offsetTop=k.top;i.offsetLeft=k.left;i.nodes=e.unique(a.rows({page:"current"}).nodes().toArray());this._cachePositions();this._clone(c);this._clonePosition(d);this.dom.target=c;c.addClass("dt-rowReorder-moving");e(g).on("mouseup.rowReorder touchend.rowReorder",function(a){b._mouseUp(a)}).on("mousemove.rowReorder touchmove.rowReorder",function(a){b._mouseMove(a)});e(f).width()===e(g).width()&&e(g.body).addClass("dt-rowReorder-noOverflow");
a=this.dom.dtScroll;this.s.scroll={windowHeight:e(f).height(),windowWidth:e(f).width(),dtTop:a.length?a.offset().top:null,dtLeft:a.length?a.offset().left:null,dtHeight:a.length?a.outerHeight():null,dtWidth:a.length?a.outerWidth():null}},_mouseMove:function(d){this._clonePosition(d);for(var c=this._eventToPage(d,"Y")-this.s.bodyTop,b=this.s.middles,a=null,i=this.s.dt,k=i.table().body(),g=0,f=b.length;g<f;g++)if(c<b[g]){a=g;break}null===a&&(a=b.length);if(null===this.s.lastInsert||this.s.lastInsert!==
a)0===a?this.dom.target.prependTo(k):(c=e.unique(i.rows({page:"current"}).nodes().toArray()),a>this.s.lastInsert?this.dom.target.insertAfter(c[a-1]):this.dom.target.insertBefore(c[a])),this._cachePositions(),this.s.lastInsert=a;this._shiftScroll(d)},_mouseUp:function(d){var c=this,b=this.s.dt,a,i,k=this.c.dataSrc;this.dom.clone.remove();this.dom.clone=null;this.dom.target.removeClass("dt-rowReorder-moving");e(g).off(".rowReorder");e(g.body).removeClass("dt-rowReorder-noOverflow");clearInterval(this.s.scrollInterval);
this.s.scrollInterval=null;var f=this.s.start.nodes,m=e.unique(b.rows({page:"current"}).nodes().toArray()),j={},h=[],l=[],o=this.s.getDataFn,n=this.s.setDataFn;a=0;for(i=f.length;a<i;a++)if(f[a]!==m[a]){var p=b.row(m[a]).id(),t=b.row(m[a]).data(),q=b.row(f[a]).data();p&&(j[p]=o(q));h.push({node:m[a],oldData:o(t),newData:o(q),newPosition:a,oldPosition:e.inArray(m[a],f)});l.push(m[a])}var r=[h,{dataSrc:k,nodes:l,values:j,triggerRow:b.row(this.dom.target),originalEvent:d}];this._emitEvent("row-reorder",
r);var s=function(){if(c.c.update){a=0;for(i=h.length;a<i;a++){var d=b.row(h[a].node).data();n(d,h[a].newData);b.columns().every(function(){this.dataSrc()===k&&b.cell(h[a].node,this.index()).invalidate("data")})}c._emitEvent("row-reordered",r);b.draw(!1)}};this.c.editor?(this.c.enable=!1,this.c.editor.edit(l,!1,e.extend({submit:"changed"},this.c.formOptions)).multiSet(k,j).one("preSubmitCancelled.rowReorder",function(){c.c.enable=!0;c.c.editor.off(".rowReorder");b.draw(!1)}).one("submitUnsuccessful.rowReorder",
function(){b.draw(!1)}).one("submitSuccess.rowReorder",function(){s()}).one("submitComplete",function(){c.c.enable=!0;c.c.editor.off(".rowReorder")}).submit()):s()},_shiftScroll:function(d){var c=this,b=this.s.scroll,a=!1,e=d.pageY-g.body.scrollTop,f,h;65>e?f=-5:e>b.windowHeight-65&&(f=5);null!==b.dtTop&&d.pageY<b.dtTop+65?h=-5:null!==b.dtTop&&d.pageY>b.dtTop+b.dtHeight-65&&(h=5);f||h?(b.windowVert=f,b.dtVert=h,a=!0):this.s.scrollInterval&&(clearInterval(this.s.scrollInterval),this.s.scrollInterval=
null);!this.s.scrollInterval&&a&&(this.s.scrollInterval=setInterval(function(){if(b.windowVert)g.body.scrollTop=g.body.scrollTop+b.windowVert;if(b.dtVert){var a=c.dom.dtScroll[0];if(b.dtVert)a.scrollTop=a.scrollTop+b.dtVert}},20))}});j.defaults={dataSrc:0,editor:null,enable:!0,formOptions:{},selector:"td:first-child",snapX:!1,update:!0,excludedChildren:"a"};var l=e.fn.dataTable.Api;l.register("rowReorder()",function(){return this});l.register("rowReorder.enable()",function(d){d===n&&(d=!0);return this.iterator("table",
function(c){c.rowreorder&&(c.rowreorder.c.enable=d)})});l.register("rowReorder.disable()",function(){return this.iterator("table",function(d){d.rowreorder&&(d.rowreorder.c.enable=!1)})});j.version="1.2.6";e.fn.dataTable.RowReorder=j;e.fn.DataTable.RowReorder=j;e(g).on("init.dt.dtr",function(d,c){if("dt"===d.namespace){var b=c.oInit.rowReorder,a=h.defaults.rowReorder;if(b||a)a=e.extend({},b,a),!1!==b&&new j(c,a)}});return j});

using Microsoft.Web.Administration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Management;
using System.Reflection;
using System.Threading;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;

namespace SiMS.AdminPanel
{
    public partial class index_page : System.Web.UI.Page
    {
        public int disc_size = 0;
        public int disc_used = 0;
        public int disc_free = 0;
        public int disc_free_perc = 0;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                disc_usage();
                bg_status();
                bind_stats();

                app_pool_status("instance");
                app_pool_status("student");
                app_pool_status("apply");
                app_pool_status("parent");

                span_time.InnerHtml = DateTime.Now.ToString("dd MMM yyyy hh:mm:ss");

                bool adim_release = release_check("admin", span_version_admin);
                btn_admin_new.Visible = adim_release;

                bool instance_release = release_check("instance", span_version_instance);
                btn_instance_new.Visible = instance_release;

                bool student_release = release_check("student", span_version_student);
                btn_student_new.Visible = student_release;

                bool apply_release = release_check("apply", span_version_apply);
                btn_apply_new.Visible = apply_release;

                bool parent_release = release_check("parent", span_version_parent);
                btn_parent_portal_new.Visible = parent_release;

                bool there_is_a_new_release = (adim_release || instance_release || student_release || parent_release);
                if (there_is_a_new_release) alert.growl.warning = "There are some updates that need to be applied. Please click on the button to update accordingly";
                timer_monitor_bgwoker.Enabled = !there_is_a_new_release;
            }
        }

        // This method is used to convert datatable to json string
        public string geo_coverage_map_json()
        {
            string sp_name = "sims.usp_instances_by_locality";
            SqlParameter[] sp_params = null;

            DataTable tbl = db.fetch.table(sp_name, sp_params);
            System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            List<Dictionary<string, object>> rows = new List<Dictionary<string, object>>();
            Dictionary<string, object> row;
            foreach (DataRow dr in tbl.Rows)
            {
                row = new Dictionary<string, object>();
                foreach (DataColumn col in tbl.Columns)
                {
                    row.Add(col.ColumnName, dr[col]);
                }
                rows.Add(row);
            }
            return serializer.Serialize(rows);
        }

        private void disc_usage()
        {
            try
            {
                // Create a DriveInfo instance of the D:\ drive
                DirectoryInfo dInfo = new DirectoryInfo(Server.MapPath("~/"));
                DriveInfo dDrive = new DriveInfo(dInfo.Root.FullName);

                // When the drive is accessible..
                if (dDrive.IsReady)
                {
                    disc_size = Convert.ToInt32(dDrive.TotalSize / 1000000000);
                    disc_used = Convert.ToInt32((dDrive.TotalSize - dDrive.AvailableFreeSpace) / 1000000000);
                    disc_free = Convert.ToInt32(dDrive.AvailableFreeSpace / 1000000000);
                    // Calculate the percentage free space
                    disc_free_perc = Convert.ToInt32((dDrive.AvailableFreeSpace / (float)dDrive.TotalSize) * 100);

                    li_disc_size.InnerHtml = disc_size.ToString() + " GB - " + dDrive.Name + " " + dDrive.DriveFormat;
                    li_disc_used.InnerHtml = disc_used.ToString() + " GB used";
                    li_disc_free.InnerHtml = disc_free.ToString() + " GB (" + disc_free_perc.ToString() + "%) free";
                    //Console.WriteLine("Drive: {0} ({1}, {2})",  dDrive.Name, dDrive.DriveFormat, dDrive.DriveType);

                    //Console.WriteLine("\tFree space:\t{0}",  dDrive.AvailableFreeSpace);
                    //Console.WriteLine("\tTotal space:\t{0}",  dDrive.TotalSize);
                    //Console.WriteLine("\n\tPercentage free space: {0:0.00}%.", freeSpacePerc);
                }
            }
            catch (Exception ex)
            {
                alert.toastr.error = "Error displaying Disc Usage Data<br />" + ex.Message;
            }
        }

        private void app_pool_status(string module_name)
        {
            HtmlGenericControl i_app_pool = div_modules.FindControl("i_app_pool_" + module_name) as HtmlGenericControl;
            HtmlContainerControl span_version = div_modules.FindControl("span_version_" + module_name) as HtmlContainerControl;
            try
            {
                if (i_app_pool == null) return;
                i_app_pool.Attributes["class"] = "fas fa-globe text-danger";

                string app_pool_name = "sims_" + module_name.ToLower();
                var server = new ServerManager();
                var app_pool = server.ApplicationPools.FirstOrDefault(iis_appool => iis_appool.Name == app_pool_name);
                if (app_pool == null) return;

                i_app_pool.Attributes["title"] = app_pool.State.ToString();
                switch (app_pool.State)
                {
                    case ObjectState.Starting:
                        i_app_pool.Attributes["class"] = "fas fa-globe fa-spin text-warning";
                        break;

                    case ObjectState.Started:
                        i_app_pool.Attributes["class"] = "fas fa-globe fa-spin text-success";
                        break;

                    case ObjectState.Stopping:
                        i_app_pool.Attributes["class"] = "fas fa-globe fa-spin text-danger";
                        break;

                    case ObjectState.Stopped:
                        i_app_pool.Attributes["class"] = "fas fa-globe text-danger";
                        break;
                }

                //timer_modules.Enabled=true;
            }
            catch (Exception ex)
            {
                //timer_modules.Enabled=false;
                alert.toastr.error = ex.Message;
                span_version.InnerHtml = ex.Message;
            }
        }

        private void bg_status()
        {
            i_bg_worker.Attributes["class"] = "fas fa-cog";
            span_bg_worker_iholder.Attributes["class"] = "info-box-icon bg-danger elevation-1";
            span_bg_workder_status.InnerHtml = "Not running";
            try
            {
                string host = Session["host"].ToString().ToLower();
                Process[] procs = Process.GetProcessesByName("bgworker");
                if (procs.Length > 0)
                {
                    foreach (Process p in procs)
                    {
                        //Response.Write(p.ProcessName+"\r\n");
                        string cmdLine = "";
                        string query = $@"SELECT CommandLine FROM Win32_Process  WHERE ProcessId = {p.Id}";

                        using (var searcher = new ManagementObjectSearcher(query))
                        using (var collection = searcher.Get())
                        {
                            var managementObject = collection.OfType<ManagementObject>().FirstOrDefault();
                            cmdLine = (managementObject != null) ? (string)managementObject["CommandLine"] : "";
                        }
                        //Response.Write(" - "+cmdLine+"\r\n");

                        if (cmdLine != "")
                        {
                            if (cmdLine.ToLower().IndexOf(host) > 0)
                            {
                                i_bg_worker.Attributes["class"] = "fas fa-cog fa-spin";
                                span_bg_worker_iholder.Attributes["class"] = "info-box-icon bg-success elevation-1";
                                span_bg_workder_status.InnerHtml = host + " - ID " + p.Id.ToString() + " - " + memory.ToSize(p.PagedMemorySize64, memory.memorySizeUnits.MB).ToString() + " MB<br />" + DateTime.Now.ToString("HH:mm:ss");

                                return;
                            }
                        }
                        else
                        {
                            i_bg_worker.Attributes["class"] = "fas fa-cog";
                            span_bg_worker_iholder.Attributes["class"] = "info-box-icon bg-danger elevation-1";
                            span_bg_workder_status.InnerHtml = "CMDLINE not found";
                        }
                    }
                }
                else
                {
                    i_bg_worker.Attributes["class"] = "fas fa-cog";
                    span_bg_worker_iholder.Attributes["class"] = "info-box-icon bg-danger elevation-1";
                    span_bg_workder_status.InnerHtml = "Proc not found";
                }
            }
            catch (Exception ex)
            {
                alert.toastr.error = "Error checking BG-WORKER Status<br />" + ex.Message;
            }
        }

        private void bind_stats()
        {
            try
            {
                string sp_name = "sims.usp_stats";
                SqlParameter[] sp_params = null;
                DataSet ds = db.fetch.dataset(sp_name, sp_params);
                int instances = (int)ds.Tables[0].Rows[0]["instances"];
                if (instances != 1)
                    span_instance_count.InnerHtml = instances.ToString() + " Instances";
                else span_instance_count.InnerHtml = instances.ToString() + " Instance";

                h5_students_count.InnerHtml = ds.Tables[0].Rows[0]["students"].ToString();
                h5_staff_count.InnerHtml = ds.Tables[0].Rows[0]["staff"].ToString();
                h5_parents_count.InnerHtml = ds.Tables[0].Rows[0]["parents"].ToString();
                h5_campuses_count.InnerHtml = ds.Tables[0].Rows[0]["campuses"].ToString();

                rpt_instances_by_status.DataSource = ds.Tables[1];
                rpt_instances_by_status.DataBind();
            }
            catch (Exception ex)
            {
                alert.toastr.error = "Error displaying STATS<br />" + ex.Message;
            }
        }

        protected void timer_stats_Tick(object sender, EventArgs e)
        {
            bind_stats();
        }

        private bool release_check(string assembly_name, HtmlContainerControl span_version)
        {
            try
            {
                String current_ver = "";
                bool ret = false;
                string this_folder = Server.MapPath("~/");
                DirectoryInfo this_folder_info = new DirectoryInfo(this_folder);
                string current_dll_file_path = this_folder_info.Parent.FullName + "\\_" + assembly_name + "\\bin\\" + assembly_name + ".dll";

                //string current_txt_file_path = this_folder_info.Parent.FullName + "\\_" + assembly_name + "\\txt\\release_notes.txt";

                if (File.Exists(current_dll_file_path))
                {
                    try
                    {
                        current_ver = AssemblyName.GetAssemblyName(current_dll_file_path).Version.ToString();
                        FileInfo dll_file_info = new FileInfo(current_dll_file_path);
                        span_version.InnerHtml = current_ver.ToString() + " - " + dll_file_info.LastWriteTime.ToString("yyyy.MM.dd hh:mm");
                    }
                    catch (Exception ex)
                    {
                        current_ver = "NOT FOUND";
                        //if (File.Exists(current_txt_file_path))
                        //{
                        //    FileInfo txti = new FileInfo(current_txt_file_path);
                        //    current_ver = txti.LastWriteTime.ToString("yyyy.MM.dd.HH.mm");
                        //}
                        alert.toastr.error = "Error reading assembly info " + assembly_name.ToUpper() + ": " + ex.Message;
                    }
                }
                else
                {
                    alert.toastr.error = "Cannot read file: " + current_dll_file_path.ToAbsoluteUrl();
                }

                string new_bin_file_path = this_folder_info.Parent.FullName + "\\_release\\_" + assembly_name + "\\bin\\" + assembly_name + ".dll";
                //string new_release_notes_file_path = this_folder_info.Parent.FullName + "\\_release\\_" + assembly_name + "\\txt\\release_notes.txt";

                if (File.Exists(new_bin_file_path))
                {
                    FileInfo fi_dll = new FileInfo(new_bin_file_path);
                    //FileInfo fi_txt = new FileInfo(new_release_notes_file_path);
                    String new_ver = "";


                    FileInfo bini = new FileInfo(new_bin_file_path);
                    new_ver = bini.LastWriteTime.ToString("yyyy.MM.dd.HH.mm");


                    if (fi_dll.CreationTime < DateTime.Now.AddMinutes(-1)) ret = true;
                }

                return ret;
            }
            catch (Exception ex)
            {
                alert.toastr.error = "Release-Ckeck Error for " + assembly_name + "<br />" + ex.Message;
                return false;
            }
        }

        protected void btn_update_Click(object sender, EventArgs e)
        {
            LinkButton btn = sender as LinkButton;
            string module_name = btn.CommandArgument;
            DirectoryInfo dir_info = new DirectoryInfo(Server.MapPath("~/"));
            h4_apply_update_title.InnerHtml = (Session["host"].ToString() + "/" + module_name).ToLower();
            btn_apply_update.CommandArgument = btn.CommandArgument;
            string new_release_notes_file_path = dir_info.Parent.FullName + "\\_release\\_" + module_name + "\\txt\\release_notes.txt";
            if (File.Exists(new_release_notes_file_path))
                txt_apply_update_notes.Text = File.ReadAllText(new_release_notes_file_path);
            else txt_apply_update_notes.Visible = false;
            fn.show_modal("modal-apply-updates");
        }

        protected void btn_apply_update_Click(object sender, EventArgs e)
        {
            LinkButton btn = sender as LinkButton;
            string module_name = btn.CommandArgument;
            span_h4_post_apply_module_name.InnerText = module_name.ToUpper();
            div_post_apply_notes.InnerHtml = "<ol>";

            string this_folder = Server.MapPath("~/");
            DirectoryInfo this_folder_info = new DirectoryInfo(this_folder);
            string release_folder_path = this_folder_info.Parent.FullName + "\\_release\\_" + module_name + "\\";
            string app_folder_path = this_folder_info.Parent.FullName + "\\_" + module_name + "";
            string app_folder_bin_path = this_folder_info.Parent.FullName + "\\_" + module_name + "\\bin";
            if (!Directory.Exists(app_folder_path)) Directory.CreateDirectory(app_folder_path);

            DirectoryInfo release_folder_info = new DirectoryInfo(release_folder_path);
            DirectoryInfo app_folder_bin_info = new DirectoryInfo(app_folder_bin_path);
            DirectoryInfo app_folder_info = new DirectoryInfo(app_folder_path);
            //Copy new files

            string ipString = System.Web.HttpContext.Current.Request.UserHostAddress;
            if (Request.Url.Host.ToLower() == "localhost")
            {
                alert.growl.error = "You are doing this on a local machine! Cannot happen";
                return;
            }
            else
            {
                bool ret = false;

                if (module_name == "admin")
                {
                    ret = shell.xcopy(release_folder_path, app_folder_path);
                    if (ret)
                    {
                        shell.RecursiveDelete(release_folder_info, 0, true);
                        fn.http_redirect("~/session_timeout.aspx", false);
                    }
                }
                else
                {
                    string release_offline_file_path = Server.MapPath("~/_tpl/app_offline.htm");
                    string app_offline_file_path = app_folder_path + "\\app_offline.htm";
                    File.Copy(release_offline_file_path, app_offline_file_path, true);
                    div_post_apply_notes.InnerHtml += "<li>" + release_offline_file_path + " Copied to " + app_offline_file_path + "</li>";

                    string app_pool_name = "sims_" + module_name.ToLower();
                    var server = new ServerManager();
                    var app_pool = server.ApplicationPools.FirstOrDefault(iis_appool => iis_appool.Name == app_pool_name);
                    if (app_pool != null)
                    {
                        if (app_pool.State == ObjectState.Starting || app_pool.State == ObjectState.Started)
                        {
                            app_pool.Stop();
                            div_post_apply_notes.InnerHtml += "<li>Stopped IIS Application pool: " + app_pool.Name + "</li>";
                        }
                        else
                        {
                            div_post_apply_notes.InnerHtml += "<li>IIS Application pool: " + app_pool_name + " " + app_pool.State.ToString() + "</li>";
                        }
                        int i = 1;
                        while (app_pool.State != ObjectState.Stopped)
                        {
                            if (i >= 15)
                            {
                                div_post_apply_notes.InnerHtml += "<li style='color:red'>Time-out (15 Sec) " + app_pool_name + " - not stopping</li>";
                                fn.show_modal("modal-sims-post-apply");
                                return;
                            }
                            Thread.Sleep(1000);
                            i++;
                        }
                    }
                    else
                    {
                        div_post_apply_notes.InnerHtml += "<li>IIS Application pool: " + app_pool_name + " - not found</li>";
                    }

                    shell.RecursiveDelete(app_folder_bin_info, 0, true);
                    Thread.Sleep(3000);
                    div_post_apply_notes.InnerHtml += "<li>Delete folder contents: " + app_folder_bin_info.FullName + "</li>";

                    ret = shell.xcopy(release_folder_path, app_folder_path);
                    Thread.Sleep(3000);
                    div_post_apply_notes.InnerHtml += "<li>Copy: " + release_folder_path + " to " + app_folder_path + "</li>";

                    // Move the module's release folder to last_updated_release with date-time stamp
                    try
                    {
                        string parentReleaseFolder = Directory.GetParent(release_folder_path.TrimEnd(Path.DirectorySeparatorChar)).FullName;
                        string lastUpdatedReleaseFolder = Path.Combine(parentReleaseFolder, "last_updated_release");
                        if (!Directory.Exists(lastUpdatedReleaseFolder))
                        {
                            Directory.CreateDirectory(lastUpdatedReleaseFolder);
                        }
                        string dateTimeStamp = DateTime.Now.ToString("yyyy-MM-dd HH.mm.ss");
                        string destFolderName = dateTimeStamp + " _" + module_name;
                        string destFolderPath = Path.Combine(lastUpdatedReleaseFolder, destFolderName);
                        if (Directory.Exists(destFolderPath))
                        {
                            Directory.Delete(destFolderPath, true);
                        }
                        Directory.Move(release_folder_path, destFolderPath);
                        div_post_apply_notes.InnerHtml += $"<li>Moved: {release_folder_path} to {destFolderPath}</li>";
                    }
                    catch (Exception ex)
                    {
                        div_post_apply_notes.InnerHtml += $"<li style='color:red'>Failed to move release folder: {ex.Message}</li>";
                    }

                    if (File.Exists(app_offline_file_path))
                    {
                        File.Delete(app_offline_file_path);
                        div_post_apply_notes.InnerHtml += "<li>Deleted: " + app_offline_file_path + "</li>";
                    }
                    //shell.RecursiveDelete(release_folder_info, 0, true);
                    //Thread.Sleep(3000);
                    //div_post_apply_notes.InnerHtml+="<li>Delete folder contents: "+release_folder_info.FullName+"</li>";

                    if (app_pool != null)
                    {
                        app_pool.Start();
                        div_post_apply_notes.InnerHtml += "<li>Started IIS Application pool: " + app_pool.Name + "</li>";
                    }

                    //CHECKS
                    bool adim_release = release_check("admin", span_version_admin);
                    btn_admin_new.Visible = adim_release;

                    bool instance_release = release_check("instance", span_version_instance);
                    btn_instance_new.Visible = instance_release;

                    bool parent_release = release_check("parent", span_version_parent);
                    btn_parent_portal_new.Visible = parent_release;


                    bool student_release = release_check("student", span_version_student);
                    btn_student_new.Visible = student_release;

                    bool apply_release = release_check("apply", span_version_apply);
                    btn_apply_new.Visible = apply_release;

                    fn.show_modal("modal-sims-post-apply");
                }
            }
        }

        public static bool CopyAll(DirectoryInfo source, DirectoryInfo target)
        {
            bool ret = false;
            // Check if the target directory exists, if not, create it.
            if (Directory.Exists(target.FullName) == false)
            {
                Directory.CreateDirectory(target.FullName);
            }

            // Copy each file into it’s new directory, except files to skip.
            foreach (var fileInfo in source.GetFiles())
            {
                string copy_to = System.IO.Path.Combine(target.ToString(), fileInfo.Name);
                fileInfo.CopyTo(copy_to, true);
            }

            // Copy each subdirectory using recursion.
            foreach (var subDir in source.GetDirectories())
            {
                if (subDir.Name != target.Name)
                {
                    var nextTargetSubDir = target.CreateSubdirectory(subDir.Name);
                    CopyAll(subDir, nextTargetSubDir);
                }
            }

            ret = true;
            return ret;
        }

        protected void timer_monitor_bgwoker_Tick(object sender, EventArgs e)
        {
            bg_status();
        }

        protected void btn_restart_polling_service_Click(object sender, EventArgs e)
        {
        }

        protected void btn_stop_start_polling_service_Click(object sender, EventArgs e)
        {
        }

        protected void btn_new_bgworker_build_Click(object sender, EventArgs e)
        {
        }

        protected void btn_instance_offline_Click(object sender, EventArgs e)
        {
        }

        protected void timer_modules_Tick(object sender, EventArgs e)
        {
            app_pool_status("instance");
            app_pool_status("apply");
            app_pool_status("parent");
            app_pool_status("student");

            if (i_heartbeat.Attributes["class"] != "ti ti-circle text-success") i_heartbeat.Attributes["class"] = "ti ti-circle text-success";
            else i_heartbeat.Attributes["class"] = "ti ti-circle text-danger";

            span_time.InnerHtml = DateTime.Now.ToString("dd MMM yyyy hh:mm:ss");
        }

        protected void btn_check_new_releases_Click(object sender, EventArgs e)
        {
            try
            {
                // Check if running on localhost
                if (Request.Url.Host.ToLower() == "localhost")
                {
                    alert.growl.error = "You are doing this on a local machine! Cannot happen";
                    return;
                }

                // Show progress modal
                fn.show_modal("modal-check-releases-progress");
                div_release_progress.InnerHtml = @"
                    <div class='d-flex align-items-center mb-2'>
                        <div class='spinner-border spinner-border-sm me-2' role='status' aria-hidden='true'></div>
                        <span>Connecting to FTP server...</span>
                    </div>";

                // Step 1: Delete the current _release folder
                string this_folder = Server.MapPath("~/");
                DirectoryInfo this_folder_info = new DirectoryInfo(this_folder);
                string release_folder_path = this_folder_info.Parent.FullName + "\\_release";

                div_release_progress.InnerHtml += "<div>Removing existing _release folder...</div>";

                if (Directory.Exists(release_folder_path))
                {
                    Directory.Delete(release_folder_path, true);
                }

                // Step 2: Find the latest release on FTP server
                div_release_progress.InnerHtml += "<div>Searching for latest release on FTP server...</div>";

                string latestReleaseFile = ftp_client.FindLatestRelease();

                if (string.IsNullOrEmpty(latestReleaseFile))
                {
                    div_release_progress.InnerHtml += "<div class='text-warning'>No release files found on FTP server matching pattern _sims_release_x.x.x.zip</div>";
                    alert.growl.warning = "No release files found on FTP server matching pattern _sims_release_x.x.x.zip";
                    return;
                }

                div_release_progress.InnerHtml += $"<div class='text-success'>Found latest release: {latestReleaseFile}</div>";

                // Step 3: Download the release file to temporary folder
                string temp_folder = Path.Combine(Path.GetTempPath(), "sims_release_download");
                if (Directory.Exists(temp_folder))
                {
                    Directory.Delete(temp_folder, true);
                }
                Directory.CreateDirectory(temp_folder);

                string temp_zip_path = Path.Combine(temp_folder, latestReleaseFile);

                div_release_progress.InnerHtml += $"<div>Downloading {latestReleaseFile}...</div>";

                bool downloadSuccess = ftp_client.DownloadFile(latestReleaseFile, temp_zip_path);

                if (!downloadSuccess || !File.Exists(temp_zip_path))
                {
                    div_release_progress.InnerHtml += "<div class='text-danger'>Failed to download release file from FTP server</div>";
                    alert.growl.error = "Failed to download release file from FTP server";
                    return;
                }

                div_release_progress.InnerHtml += "<div class='text-success'>Download completed successfully</div>";

                // Step 4: Extract the zip file
                div_release_progress.InnerHtml += "<div>Extracting release files...</div>";

                using (var archive = System.IO.Compression.ZipFile.OpenRead(temp_zip_path))
                {
                    foreach (var entry in archive.Entries)
                    {
                        // Only extract files that are in the _release folder
                        if (entry.FullName.StartsWith("_release/") || entry.FullName.StartsWith("_release\\"))
                        {
                            string destinationPath = Path.Combine(this_folder_info.Parent.FullName, entry.FullName);
                            string destinationDir = Path.GetDirectoryName(destinationPath);

                            if (!Directory.Exists(destinationDir))
                            {
                                Directory.CreateDirectory(destinationDir);
                            }

                            if (!string.IsNullOrEmpty(entry.Name)) // It's a file, not a directory
                            {
                                entry.ExtractToFile(destinationPath, true);
                            }
                        }
                    }
                }

                div_release_progress.InnerHtml += "<div class='text-success'>Extraction completed successfully</div>";

                // Step 5: Clean up temporary files
                if (Directory.Exists(temp_folder))
                {
                    Directory.Delete(temp_folder, true);
                }

                // Step 6: Refresh the page to show new releases
                div_release_progress.InnerHtml += "<div class='text-success'>Release update completed! Reloading page...</div>";
                alert.growl.success = $"Successfully downloaded and extracted {latestReleaseFile}. Reloading page...";

                // Add a small delay and then redirect to refresh the page
                Response.AddHeader("Refresh", "3;url=" + Request.RawUrl);
            }
            catch (Exception ex)
            {
                div_release_progress.InnerHtml += $"<div class='text-danger'>Error: {ex.Message}</div>";
                alert.growl.error = "Error checking for new releases: " + ex.Message;
            }
        }
    }
}
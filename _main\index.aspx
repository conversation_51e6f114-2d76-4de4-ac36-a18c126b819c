<%@ Page Title="" Language="C#" MasterPageFile="~/_main/_main.master" AutoEventWireup="true" CodeBehind="index.aspx.cs" Inherits="SiMS.AdminPanel.index_page" %>

<asp:Content ID="Content0" runat="server" ContentPlaceHolderID="cph_head">
    <style type="text/css">
        html {
            height: 100%
        }

        body {
            height: 100%;
            margin: 0;
            padding: 0
        }

        #map_canvas {
            height: 100%
        }
    </style>
    <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCfTFqy7VVM-ibBApPbUP-vbauZspm7jEY">
    </script>
    <script type="text/javascript">

        function initialize_map() {
            var markers = JSON.parse('<%=this.geo_coverage_map_json() %>');
            var mapOptions = {
                center: new google.maps.LatLng(<%=Session["country_lat"].ToString()%>,<%=Session["country_lng"].ToString()%>),
                zoom: 7,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                //  marker:true
                datalessRegionColor: 'transparent'

            };
            var infoWindow = new google.maps.InfoWindow();
            var map = new google.maps.Map(document.getElementById("map_canvas"), mapOptions);

            var geocoder = new google.maps.Geocoder();

            var address = '<%=Session["capital_name"].ToString()%>';

            geocoder.geocode({
                'address': address + ', <%=Session["country_name"].ToString()%>'
            },
                function (results, status) {
                    if (status == google.maps.GeocoderStatus.OK) {
                        new google.maps.Marker({
                            position: results[0].geometry.location,
                            map: map
                        });
                    }
                });

            for (i = 0; i < markers.length; i++) {
                var data = markers[i]
                var myLatlng = new google.maps.LatLng(data.lat, data.lng);
                var marker = new google.maps.Marker({
                    position: myLatlng,
                    map: map,
                    title: data.title
                });
                (function (marker, data) {

                    // Attaching a click event to the current marker
                    google.maps.event.addListener(marker, "click", function (e) {
                        infoWindow.setContent(data.description);
                        infoWindow.open(map, marker);
                    });
                })(marker, data);
            }
        }

        google.maps.event.addDomListener(window, "load", initialize_map);
    </script>
</asp:Content>
<asp:Content ID="Content1" ContentPlaceHolderID="cph_page_title" runat="server">
    Dashboard
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_breadcrumps" runat="server">
    <li class="breadcrumb-item"><a runat="server" href="~/_main/index.aspx">General Dashboard</a></li>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cph_main" runat="server">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card p-1">
                    <%--<asp:UpdatePanel ID="update_panel_modules" runat="server" ChildrenAsTriggers="true">
                        <ContentTemplate>
                            <asp:Timer ID="timer_modules" runat="server" Interval="1000" OnTick="timer_modules_Tick" Enabled="false"></asp:Timer>--%>

                            <span style="display: block"><i id="i_heartbeat" runat="server" class="ti ti-circle text-success"></i>&nbsp;<span id="span_time" runat="server"></span></span>

                            <!-- Check for New Releases Button -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <asp:LinkButton ID="btn_check_new_releases" runat="server" OnClick="btn_check_new_releases_Click" CssClass="btn btn-primary">
                                        <i class="ti ti-download"></i> Check for New Releases
                                    </asp:LinkButton>
                                </div>
                            </div>

                            <div id="div_modules" runat="server" class="row row-cards mb-1">

                                <!-- ADMIN -->
                                <div class="col">
                                    <div class="card card-sm">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-auto">
                                                    <span class="bg-primary text-white avatar">
                                                        <i id="i_app_pool_admin" runat="server" class="ti ti-automatic-gearbox text-success"></i>
                                                    </span>
                                                </div>
                                                <div class="col">
                                                    <div class="font-weight-medium">
                                                        Admin
                                                    </div>
                                                    <div class="text-muted">
                                                        <span id="span_version_admin" runat="server" style="font-size: 10px"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <asp:LinkButton ID="btn_admin_new" runat="server" OnClick="btn_update_Click" CommandArgument="admin" CssClass="btn btn-block btn-danger mt-1">Apply updates (Admin Console)</asp:LinkButton>
                                </div>

                                <!-- BackOffice/Student Portal -->
                                <div class="col">
                                    <div class="card card-sm">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-auto">
                                                    <span class="bg-primary text-white avatar"><i id="i_app_pool_instance" runat="server" class="ti ti-buildings"></i></span>
                                                </div>
                                                <div class="col">
                                                    <div class="font-weight-medium">
                                                        Instance
                                                    </div>
                                                    <div class="text-muted">
                                                        <span id="span_version_instance" runat="server" style="font-size: 10px"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <asp:LinkButton ID="btn_instance_new" runat="server" OnClick="btn_update_Click" CommandArgument="instance" CssClass="btn btn-block btn-success mt-1">Apply updates (BackOffice)</asp:LinkButton>
                                </div>
                                
                                <!-- BackOffice/Student Portal -->
                                <div class="col">
                                    <div class="card card-sm">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-auto">
                                                    <span class="bg-primary text-white avatar"><i id="i_app_pool_student" runat="server" class="ti ti-users"></i></span>
                                                </div>
                                                <div class="col">
                                                    <div class="font-weight-medium">
                                                        Student Portal
                                                    </div>
                                                    <div class="text-muted">
                                                        <span id="span_version_student" runat="server" style="font-size: 10px"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <asp:LinkButton ID="btn_student_new" runat="server" OnClick="btn_update_Click" CommandArgument="student" CssClass="btn btn-block btn-success mt-1">Apply updates (Student)</asp:LinkButton>
                                </div>

                                <!-- Parents Portal -->
                                <div class="col">
                                    <div class="card card-sm">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-auto">
                                                    <span class="bg-primary text-white avatar"><i id="i_app_pool_parent" runat="server" class="ti ti-users-group"></i></span>
                                                </div>
                                                <div class="col">
                                                    <div class="font-weight-medium">
                                                        Parent
                                                    </div>
                                                    <div class="text-muted">
                                                        <span id="span_version_parent" runat="server" style="font-size: 10px"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <asp:LinkButton ID="btn_parent_portal_new" runat="server" OnClick="btn_update_Click" CommandArgument="parent" CssClass="btn btn-block btn-success mt-1">Apply updates (Parent Portal)</asp:LinkButton>
                                </div>


                                <!-- Online Applications -->
                                <div class="col">
                                    <div class="card card-sm">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-auto">
                                                    <span class="bg-primary text-white avatar"><i id="i_app_pool_apply" runat="server" class="ti ti-pacman"></i></span>
                                                </div>
                                                <div class="col">
                                                    <div class="font-weight-medium">
                                                        Apply
                                                    </div>
                                                    <div class="text-muted">
                                                        <span id="span_version_apply" runat="server" style="font-size: 10px"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <asp:LinkButton ID="btn_apply_new" runat="server" OnClick="btn_update_Click" CommandArgument="apply" CssClass="btn btn-block btn-success mt-1">Apply updates (Online Applications)</asp:LinkButton>
                                </div>

                            </div>
                        <%--</ContentTemplate>
                        <Triggers>
                            <asp:AsyncPostBackTrigger ControlID="timer_modules" EventName="Tick" />
                        </Triggers>
                    </asp:UpdatePanel>
                     --%>

                </div>
            </div>
        </div>

        <div class="row mt-3">

            <!-- BG WORKER -->
            <div class="col-12 col-sm-12 col-md-3">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server" ChildrenAsTriggers="true">
                    <ContentTemplate>
                        <asp:Timer ID="timer_monitor_bgwoker" runat="server" Interval="1000" OnTick="timer_monitor_bgwoker_Tick" Enabled="true"></asp:Timer>

                        <div class="card card-sm mb-1">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <span id="span_bg_worker_iholder" runat="server" class="bg-primary text-white avatar">
                                            <i id="i_bg_worker" runat="server" class="fas fa-cog fa-spin"></i>
                                        </span>
                                    </div>
                                    <div class="col">
                                        <div class="font-weight-medium">BG-WORKER</div>
                                        <div class="text-muted">
                                            <span id="span_bg_workder_status" runat="server" style="font-size: 10px"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ContentTemplate>
                    <Triggers>
                        <asp:AsyncPostBackTrigger ControlID="timer_monitor_bgwoker" EventName="Tick" />
                    </Triggers>
                </asp:UpdatePanel>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Disc Usage</h3>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="chart-responsive">
                                    <canvas id="pieChart" height="150"></canvas>
                                </div>
                                <!-- ./chart-responsive -->
                            </div>
                            <!-- /.col -->
                            <div class="col-md-6">
                                <ul class="chart-legend clearfix">
                                    <li><i class="far fa-circle mr-1"></i><span id="li_disc_size" runat="server"></span></li>
                                    <li><i class="far fa-circle mr-1 text-danger"></i><span id="li_disc_used" runat="server"></span></li>
                                    <li><i class="far fa-circle mr-1 text-primary"></i><span id="li_disc_free" runat="server"></span></li>
                                </ul>
                            </div>
                            <!-- /.col -->
                        </div>
                        <!-- /.row -->
                    </div>
                </div>

            </div>

            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">General Statistics (Prototype)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <p class="text-center">
                                    <strong>Usage: 03 Dec, 2022 - 03 Jan, 2023</strong>
                                </p>

                                <div class="chart">
                                    <canvas id="salesChart" height="180" style="height: 180px;"></canvas>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <p class="text-center">
                                    <strong><span id="span_instance_count" runat="server"></span></strong>
                                </p>
                                <asp:Repeater ID="rpt_instances_by_status" runat="server">
                                    <ItemTemplate>

                                        <div class="progress-group">
                                            <%# DataBinder.Eval(Container.DataItem, "status")%>
                                            <span class="float-right"><b><%# DataBinder.Eval(Container.DataItem, "cnt")%></b></span>
                                            <div class="progress progress-sm">
                                                <div class="progress-bar bg-<%# DataBinder.Eval(Container.DataItem, "bg")%>" style="width: <%# DataBinder.Eval(Container.DataItem, "percentage")%>%"></div>
                                            </div>
                                        </div>
                                    </ItemTemplate>
                                </asp:Repeater>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-sm-3 col-6">
                                <div class="description-block border-right">
                                    <h5 id="h5_students_count" runat="server" class="description-header"></h5>
                                    <span class="description-text">STUDENTS</span>
                                </div>
                            </div>
                            <div class="col-sm-3 col-6">
                                <div class="description-block border-right">
                                    <h5 id="h5_staff_count" runat="server" class="description-header"></h5>
                                    <span class="description-text">STAFF</span>
                                </div>
                            </div>
                            <div class="col-sm-3 col-6">
                                <div class="description-block border-right">
                                    <h5 id="h5_parents_count" runat="server" class="description-header"></h5>
                                    <span class="description-text">PARENTS</span>
                                </div>
                            </div>
                            <div class="col-sm-3 col-6">
                                <div class="description-block">
                                    <h5 id="h5_campuses_count" runat="server" class="description-header"></h5>
                                    <span class="description-text">CAMPASES</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Geo-Coverage</h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="map_canvas" style="height: 1000px"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- NEW RELEASE - ADMIN -->
    <div class="modal hide fade"
        id="modal-apply-updates"
        tabindex="-1"
        role="dialog"
        aria-labelledby="modal-apply-updates-title"
        aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="modal-apply-updates-title">Apply updates (<span id="h4_apply_update_title" runat="server"></span>)</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form">
                        <p visible="false">
                            <span style="color: red">Please note that you will be logged out for the update to run. </span>
                        </p>
                        <asp:TextBox ID="txt_apply_update_notes" runat="server" TextMode="MultiLine" Wrap="false" CssClass="form-control" Style="min-height: 300px; font-size: 12px; text-wrap: avoid; font-family: 'Courier New', Arial; border: 0"></asp:TextBox>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn me-auto" data-bs-dismiss="modal">Close</button>
                    <asp:LinkButton ID="btn_apply_update" runat="server" CommandArgument="" OnClick="btn_apply_update_Click" CssClass="btn btn-success pull-left float-left">
                        <i class="ti ti-check"></i>APPLY UPDATE NOW
                    </asp:LinkButton>
                </div>
            </div>
        </div>
    </div>

    <!-- ABOUT - ADMIN -->
    <div class="modal hide fade"
        id="modal-about-sims-admin"
        tabindex="-1"
        role="dialog"
        aria-labelledby="modal-about-sims-admin-title"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="modal-about-sims-admin-title">SiMS Admin Console - Release Notes</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form">
                        <div id="div_admin_release_notes" runat="server" wrap="false" style="max-height: 300px; font-size: 12px; text-wrap: avoid; font-family: 'Courier New', Arial; border: 0"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn me-auto" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- ABOUT - INSTANCE -->
    <div class="modal hide fade"
        id="modal-sims-instance-release-notes"
        tabindex="-1"
        role="dialog"
        aria-labelledby="modal-about-sims-instance-title"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="modal-about-sims-instance-title">SiMS BackOffice/Student Portal Release Notes</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form">
                        <div id="div_instance_release_notes" runat="server" wrap="false" style="min-height: 100px; font-size: 12px; text-wrap: avoid; font-family: 'Courier New', Arial; border: 0"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn me-auto" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- ABOUT - PARENTS PORTAL -->
    <div class="modal hide fade"
        id="modal-sims-parent-portal-release-notes"
        tabindex="-1"
        role="dialog"
        aria-labelledby="modal-sims-parent-portal-release-notes-title"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="modal-sims-parent-portal-release-notes-title">Parents' Portal Release Notes</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form">
                        <div id="div_parent_portal_release_notes" runat="server" wrap="false" style="min-height: 100px; font-size: 12px; text-wrap: avoid; font-family: 'Courier New', Arial; border: 0"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn me-auto" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- POST APPLY -->
    <div class="modal hide fade"
        id="modal-sims-post-apply"
        tabindex="-1"
        role="dialog"
        aria-labelledby="modal-sims-post-apply-title"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="modal-sims-post-apply-title">Release-Deployment - <span id="span_h4_post_apply_module_name" runat="server"></span></h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form">
                        <div id="div_post_apply_notes" runat="server" wrap="false" style="min-height: 100px; font-size: 12px; text-wrap: avoid; font-family: 'Courier New', Arial; border: 0"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn me-auto" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- CHECK FOR NEW RELEASES PROGRESS -->
    <div class="modal hide fade"
        id="modal-check-releases-progress"
        tabindex="-1"
        role="dialog"
        aria-labelledby="modal-check-releases-progress-title"
        aria-hidden="true"
        data-bs-backdrop="static"
        data-bs-keyboard="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="modal-check-releases-progress-title">Checking for New Releases</h4>
                </div>
                <div class="modal-body">
                    <div class="form">
                        <div id="div_release_progress" runat="server" style="min-height: 100px; font-size: 12px; font-family: 'Courier New', Arial;">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
                                <span>Connecting to FTP server...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cph_footer_scripts" runat="server">
    <script src="../assets/plugins/jquery-mousewheel/jquery.mousewheel.js"></script>
    <script src="../assets/plugins/raphael/raphael.min.js"></script>
    <script src="../assets/plugins/jquery-mapael/jquery.mapael.min.js"></script>
    <%--<script src="../assets/plugins/jquery-mapael/maps/usa_states.min.js"></script>--%>
    <script src="../assets/plugins/chart.js/Chart.min.js"></script>
    <%--<script src="../assets/js/pages/dashboard2.js"></script>--%>
    <script>
        $(function () {
            var pieChartCanvas = $('#pieChart').get(0).getContext('2d')
            var pieData = {
                labels: [
                    'Free',
                    'Used'
                ],
                datasets: [
                    {
                        data: [<%=disc_free.ToString()%>, <%=disc_used.ToString()%>],
                        backgroundColor: ['#0C74FA', '#FF0000'],
                    }
                ]
            }
            var pieOptions = {
                legend: {
                    display: false
                }
            }
            //Create pie or douhnut chart
            // You can switch between pie and douhnut using the method below.
            var pieChart = new Chart(pieChartCanvas, {
                type: 'doughnut',
                data: pieData,
                options: pieOptions
            });
        });
    </script>
</asp:Content>

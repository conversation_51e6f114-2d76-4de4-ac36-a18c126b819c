using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;

namespace SiMS.AdminPanel
{
    public static class ftp_client
    {
        private static string GetFtpServer()
        {
            return ConfigurationManager.AppSettings["ftp_release_server"];
        }

        private static string GetFtpUsername()
        {
            return ConfigurationManager.AppSettings["ftp_release_username"];
        }

        private static string GetFtpPassword()
        {
            return ConfigurationManager.AppSettings["ftp_release_password"];
        }

        private static string GetFtpPath()
        {
            return ConfigurationManager.AppSettings["ftp_release_path"];
        }

        /// <summary>
        /// Lists all files in the FTP directory
        /// </summary>
        /// <returns>List of file names</returns>
        public static List<string> ListFiles()
        {
            List<string> files = new List<string>();
            
            try
            {
                string ftpServer = GetFtpServer();
                string ftpPath = GetFtpPath();
                string ftpUsername = GetFtpUsername();
                string ftpPassword = GetFtpPassword();

                if (string.IsNullOrEmpty(ftpServer) || string.IsNullOrEmpty(ftpPath))
                {
                    throw new Exception("FTP server configuration is missing in web.config");
                }

                string ftpUrl = ftpServer.TrimEnd('/') + ftpPath;
                
                FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpUrl);
                request.Method = WebRequestMethods.Ftp.ListDirectory;
                request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                request.UsePassive = true;
                request.UseBinary = true;
                request.KeepAlive = false;

                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                {
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            files.Add(line.Trim());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error listing FTP files: {ex.Message}", ex);
            }

            return files;
        }

        /// <summary>
        /// Finds the latest release file matching the pattern _sims_release_x.x.x.zip
        /// </summary>
        /// <returns>The filename of the latest release or null if none found</returns>
        public static string FindLatestRelease()
        {
            try
            {
                List<string> files = ListFiles();
                
                // Pattern to match _sims_release_x.x.x.zip files
                Regex releasePattern = new Regex(@"^_sims_release_(\d+)\.(\d+)\.(\d+)\.zip$", RegexOptions.IgnoreCase);
                
                var releaseFiles = files
                    .Where(f => releasePattern.IsMatch(f))
                    .Select(f => new
                    {
                        FileName = f,
                        Match = releasePattern.Match(f),
                        Version = new Version(
                            int.Parse(releasePattern.Match(f).Groups[1].Value),
                            int.Parse(releasePattern.Match(f).Groups[2].Value),
                            int.Parse(releasePattern.Match(f).Groups[3].Value)
                        )
                    })
                    .OrderByDescending(x => x.Version)
                    .FirstOrDefault();

                return releaseFiles?.FileName;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error finding latest release: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Downloads a file from the FTP server to a local path
        /// </summary>
        /// <param name="remoteFileName">Name of the file on the FTP server</param>
        /// <param name="localFilePath">Local path where the file should be saved</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool DownloadFile(string remoteFileName, string localFilePath)
        {
            try
            {
                string ftpServer = GetFtpServer();
                string ftpPath = GetFtpPath();
                string ftpUsername = GetFtpUsername();
                string ftpPassword = GetFtpPassword();

                if (string.IsNullOrEmpty(ftpServer) || string.IsNullOrEmpty(ftpPath))
                {
                    throw new Exception("FTP server configuration is missing in web.config");
                }

                string ftpUrl = ftpServer.TrimEnd('/') + ftpPath.TrimEnd('/') + "/" + remoteFileName;
                
                FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpUrl);
                request.Method = WebRequestMethods.Ftp.DownloadFile;
                request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                request.UsePassive = true;
                request.UseBinary = true;
                request.KeepAlive = false;

                // Create directory if it doesn't exist
                string directory = Path.GetDirectoryName(localFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                using (Stream responseStream = response.GetResponseStream())
                using (FileStream fileStream = new FileStream(localFilePath, FileMode.Create))
                {
                    responseStream.CopyTo(fileStream);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error downloading file '{remoteFileName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the file size of a remote file
        /// </summary>
        /// <param name="remoteFileName">Name of the file on the FTP server</param>
        /// <returns>File size in bytes</returns>
        public static long GetFileSize(string remoteFileName)
        {
            try
            {
                string ftpServer = GetFtpServer();
                string ftpPath = GetFtpPath();
                string ftpUsername = GetFtpUsername();
                string ftpPassword = GetFtpPassword();

                string ftpUrl = ftpServer.TrimEnd('/') + ftpPath.TrimEnd('/') + "/" + remoteFileName;
                
                FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpUrl);
                request.Method = WebRequestMethods.Ftp.GetFileSize;
                request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                request.UsePassive = true;
                request.UseBinary = true;
                request.KeepAlive = false;

                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.ContentLength;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting file size for '{remoteFileName}': {ex.Message}", ex);
            }
        }
    }
}

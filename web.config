﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections />
	<connectionStrings>
		<clear />
		<add name="db_connection" connectionString="Data Source=uat.eduplus.africa,2016;Initial Catalog=sims;Integrated Security=SSPI;TransparentNetworkIPResolution=True;Max Pool Size=32767; Connection Timeout=5;Pooling=true" providerName="System.Data.SqlClient" />
	</connectionStrings>
	<appSettings>
		<add key="app_title" value="SiMS" />
		<add key="svc_database" value="MSSQLSERVER" />
		<add key="svc_iis" value="W3SVC" />
		<add key="svc_ax" value="AxMmSvc" />
		<add key="https_only" value="true" />
		<add key="svc_reset_bat" value="d:\SiMS\exec\reset.services.bat" />
		<add key="licence_servers" value="localhost,sims.com.na,sims.connect.com.na,sims.cnx.technology,na.eduplus.africa" />
		<add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
		<add key="webpages:Enabled" value="true" />
		<!-- FTP Release Server Configuration -->
		<add key="ftp_release_server" value="uat.eduplus.adrica" />
		<add key="ftp_release_username" value="connect" />
		<add key="ftp_release_password" value="$ungura!Music@2024" />
		<add key="ftp_release_path" value="/sims/_release/" />
	</appSettings>
	<!--
	For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.
	The following attributes can be set on the <httpRuntime> tag.
	  <system.Web>
		<httpRuntime targetFramework="4.6.1" />
	  </system.Web>
  -->

	<system.web>
		<globalization culture="en-US" uiCulture="en" />
		<compilation debug="true" targetFramework="4.8">
			<assemblies>
				<add assembly="System.Web.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
			</assemblies>
		</compilation>
		<httpRuntime executionTimeout="1600" maxRequestLength="18192" useFullyQualifiedRedirectUrl="false" minFreeThreads="8" minLocalRequestFreeThreads="4" appRequestQueueLimit="100" enableVersionHeader="true" requestValidationMode="2.0" />
		<pages buffer="true" enableEventValidation="false" validateRequest="false" controlRenderingCompatibilityVersion="4.0">
			<namespaces>
				<add namespace="System.Data" />
				<add namespace="System.Text" />
			</namespaces>
		</pages>
		<!--<sessionState mode="InProc" cookieless="true" timeout="150" />-->
		<!--
		<httpModules>
			<add type="DosAttackModule" name="DosAttackModule" />
		</httpModules>-->
	</system.web>
	<system.webServer>
		<httpErrors errorMode="DetailedLocalOnly" existingResponse="PassThrough" />
		<asp scriptErrorSentToBrowser="true" />
		<validation validateIntegratedModeConfiguration="false" />
		<modules runAllManagedModulesForAllRequests="true" />
		<httpProtocol>
			<customHeaders>
				<add name="Access-Control-Allow-Origin" value="*" />
				<add name="Access-Control-Allow-Headers" value="Content-Type" />
				<add name="Access-Control-Allow-Methods" value="GET,POST,PUT,DELETE,OPTIONS" />
				<add name="Access-Control-Allow-Credentials" value="true" />
			</customHeaders>
		</httpProtocol>
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="1073741824" />
			</requestFiltering>
		</security>
		<handlers>
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
		<staticContent>
			<remove fileExtension=".woff" />
			<remove fileExtension=".woff2" />
			<mimeMap fileExtension=".woff" mimeType="application/x-font-woff" />
			<remove fileExtension=".woff2" />
			<mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />

			<!-- Add WebP MIME type -->
			<remove fileExtension=".webp" />
			<mimeMap fileExtension=".webp" mimeType="image/webp" />
		</staticContent>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Formats.Asn1" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Security.Principal.Windows" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ServiceProcess.ServiceController" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Win32.Registry" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Reflection.TypeExtensions" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.3.0" newVersion="6.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.4.0" newVersion="4.2.4.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.1.6.0" newVersion="4.1.6.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.HashCode" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>
{"version": 3, "sources": ["../../../js/i18n/defaults-hr_HR.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;AAC1C,IAAI,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;AACpD,IAAI,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC1D,MAAM,MAAM,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;AACvF,IAAI,EAAE,CAAC;AACP,IAAI,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,MAAM,CAAC,CAAC,CAAC;AACf,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC;AACnH,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;AACnI,MAAM,EAAE,CAAC;AACT,IAAI,EAAE,CAAC;AACP,IAAI,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;AACrC,IAAI,eAAe,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACzC,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-hr_HR.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: 'Odaberite stavku',\r\n    noneResultsText: 'Nema rezultata pretrage {0}',\r\n    countSelectedText: function (numSelected, numTotal) {\r\n      return (numSelected == 1) ? '{0} stavka selektirana' : '{0} stavke selektirane';\r\n    },\r\n    maxOptionsText: function (numAll, numGroup) {\r\n      return [\r\n        (numAll == 1) ? 'Limit je postignut ({n} stvar maximalno)' : 'Limit je postignut ({n} stavke maksimalno)',\r\n        (numGroup == 1) ? 'Grupni limit je postignut ({n} stvar maksimalno)' : 'Grupni limit je postignut ({n} stavke maksimalno)'\r\n      ];\r\n    },\r\n    selectAllText: 'Selektiraj sve',\r\n    deselectAllText: 'Deselektiraj sve',\r\n    multipleSeparator: ', '\r\n  };\r\n})(jQuery);\r\n"]}